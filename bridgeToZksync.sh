#!/bin/bash

# Set the amount to bridge
AMOUNT=100000

# Initialize variables
SKIP_ZKSYNC=false

DEFAULT_ZKSYNC_LOCAL_KEY="0x7726827caac94a7f9e1b160f7ea819f172f7b6f9d2a97f992c38edeab82d4110"
DEFAULT_ZKSYNC_ADDRESS="0x36615Cf349d7F6344891B1e7CA7C72883F5dc049"

ZKSYNC_REGISTRY_MODULE_OWNER_CUSTOM="0x3139687Ee9938422F57933C3CDB3E21EE43c4d0F"
ZKSYNC_TOKEN_ADMIN_REGISTRY="0xc7777f12258014866c677Bdb679D0b007405b7DF"
ZKSYNC_ROUTER="0xA1fdA8aa9A8C4b945C45aD30647b01f07D7A0B16"
ZKSYNC_RNM_PROXY_ADDRESS="0x3DA20FD3D8a8f8c1f1A5fD03648147143608C467"
ZKSYNC_SEPOLIA_CHAIN_SELECTOR="6898391096552792247"
ZKSYNC_LINK_ADDRESS="0x23A1aFD896c8c8876AF46aDc38521f4432658d1e"

SEPOLIA_REGISTRY_MODULE_OWNER_CUSTOM="0x62e731218d0D47305aba2BE3751E7EE9E5520790"
SEPOLIA_TOKEN_ADMIN_REGISTRY="0x95F29FEE11c5C55d26cCcf1DB6772DE953B37B82"
SEPOLIA_ROUTER="0x0BF3dE8c5D3e8A2B34D2BEeB17ABfCeBaf363A59"
SEPOLIA_RNM_PROXY_ADDRESS="0xba3f6251de62dED61Ff98590cB2fDf6871FbB991"
SEPOLIA_CHAIN_SELECTOR="16015286601757825753"
SEPOLIA_LINK_ADDRESS="0x779877A7B0D9E8603169DdbD7836e478b4624789"

# Install and setup ZKsync Foundry
echo "Checking ZKsync Foundry installation..."

# Check if forge with zksync support is already available
if command -v forge &> /dev/null && forge create --help | grep -q "zksync"; then
    echo "ZKsync Foundry already installed and available"
elif command -v foundryup-zksync &> /dev/null; then
    echo "foundryup-zksync found, updating to latest version..."
    if ! foundryup-zksync; then
        echo "ERROR: foundryup-zksync failed"
        echo "Skipping ZKsync deployment and proceeding with Sepolia-only operations"
        SKIP_ZKSYNC=true
    fi
else
    echo "foundryup-zksync not found, installing..."
    curl -L https://raw.githubusercontent.com/matter-labs/foundry-zksync/main/install-foundry-zksync | bash
    source ~/.bashrc
    if ! foundryup-zksync; then
        echo "ERROR: Failed to install foundryup-zksync"
        echo "Skipping ZKsync deployment and proceeding with Sepolia-only operations"
        SKIP_ZKSYNC=true
    fi
fi

# Compile and deploy the Rebase Token contract
source .env

if [ "$SKIP_ZKSYNC" != "true" ]; then
    forge build --zksync
    echo "Compiling and deploying the Rebase Token contract on ZKsync..."
    ZKSYNC_REBASE_TOKEN_ADDRESS=$(forge create src/RebaseToken.sol:RebaseToken --rpc-url ${ZKSYNC_SEPOLIA_RPC_URL} --account code023  --broadcast --legacy --zksync | awk '/Deployed to:/ {print $3}')
    echo "ZKsync rebase token address: $ZKSYNC_REBASE_TOKEN_ADDRESS"

    # Validate deployment
    if [ -z "$ZKSYNC_REBASE_TOKEN_ADDRESS" ] || [ "$ZKSYNC_REBASE_TOKEN_ADDRESS" = "" ]; then
        echo "ERROR: Failed to deploy RebaseToken on ZKsync"
        echo "This will cause subsequent operations to fail with empty addresses"
        exit 1
    fi
else
    echo "WARNING: Skipping ZKsync RebaseToken deployment"
    ZKSYNC_REBASE_TOKEN_ADDRESS=""
fi

# Compile and deploy the pool contract
if [ "$SKIP_ZKSYNC" != "true" ]; then
    echo "Compiling and deploying the pool contract on ZKsync..."
    ZKSYNC_POOL_ADDRESS=$(forge create src/RebaseTokenPool.sol:RebaseTokenPool --rpc-url ${ZKSYNC_SEPOLIA_RPC_URL} --account code023 --broadcast --legacy --constructor-args ${ZKSYNC_REBASE_TOKEN_ADDRESS} 18 "[]" ${ZKSYNC_RNM_PROXY_ADDRESS} ${ZKSYNC_ROUTER} --zksync | awk '/Deployed to:/ {print $3}')
    echo "Pool address: $ZKSYNC_POOL_ADDRESS"

    # Validate deployment
    if [ -z "$ZKSYNC_POOL_ADDRESS" ] || [ "$ZKSYNC_POOL_ADDRESS" = "" ]; then
        echo "ERROR: Failed to deploy RebaseTokenPool on ZKsync"
        echo "This will cause subsequent operations to fail with empty addresses"
        exit 1
    fi
else
    echo "WARNING: Skipping ZKsync RebaseTokenPool deployment"
    ZKSYNC_POOL_ADDRESS=""
fi

if [ "$SKIP_ZKSYNC" != "true" ]; then
    # Set the permissions for the pool contract
    echo "Setting the permissions for the pool contract on ZKsync..."
    cast send ${ZKSYNC_REBASE_TOKEN_ADDRESS} --rpc-url ${ZKSYNC_SEPOLIA_RPC_URL} --account code023 "grantMintAndBurnRole(address)" ${ZKSYNC_POOL_ADDRESS}
    echo "Pool permissions set"

    # Set the CCIP roles and permissions
    echo "Setting the CCIP roles and permissions on ZKsync..."
    cast send ${ZKSYNC_REGISTRY_MODULE_OWNER_CUSTOM} "registerAdminViaOwner(address)" ${ZKSYNC_REBASE_TOKEN_ADDRESS} --rpc-url ${ZKSYNC_SEPOLIA_RPC_URL} --account code023
    cast send ${ZKSYNC_TOKEN_ADMIN_REGISTRY} "acceptAdminRole(address)" ${ZKSYNC_REBASE_TOKEN_ADDRESS} --rpc-url ${ZKSYNC_SEPOLIA_RPC_URL} --account code023
    cast send ${ZKSYNC_TOKEN_ADMIN_REGISTRY} "setPool(address,address)" ${ZKSYNC_REBASE_TOKEN_ADDRESS} ${ZKSYNC_POOL_ADDRESS} --rpc-url ${ZKSYNC_SEPOLIA_RPC_URL} --account code023
    echo "CCIP roles and permissions set"
else
    echo "WARNING: Skipping ZKsync permissions setup"
fi

# 2. On Sepolia!

echo "Running the script to deploy the contracts on Sepolia..."
output=$(forge script ./script/Deployer.s.sol:TokenAndPoolDeployer --rpc-url ${SEPOLIA_RPC_URL} --account code023 --broadcast)
echo "Contracts deployed and permission set on Sepolia"

# Extract the addresses from the output
SEPOLIA_REBASE_TOKEN_ADDRESS=$(echo "$output" | grep 'token: contract RebaseToken' | awk '{print $4}')
SEPOLIA_POOL_ADDRESS=$(echo "$output" | grep 'pool: contract RebaseTokenPool' | awk '{print $4}')

echo "Sepolia rebase token address: $SEPOLIA_REBASE_TOKEN_ADDRESS"
echo "Sepolia pool address: $SEPOLIA_POOL_ADDRESS"

# Deploy the vault
echo "Deploying the vault on Sepolia..."
VAULT_ADDRESS=$(forge script ./script/Deployer.s.sol:VaultDeployer --rpc-url ${SEPOLIA_RPC_URL} --account code023 --broadcast --sig "run(address)" ${SEPOLIA_REBASE_TOKEN_ADDRESS} | grep 'vault: contract Vault' | awk '{print $NF}')
echo "Vault address: $VAULT_ADDRESS"

# Configure the pool on Sepolia
echo "Configuring the pool on Sepolia..."
# uint64 remoteChainSelector,
#         address remotePoolAddress, /
#         address remoteTokenAddress, /
#         bool outboundRateLimiterIsEnabled, false 
#         uint128 outboundRateLimiterCapacity, 0
#         uint128 outboundRateLimiterRate, 0
#         bool inboundRateLimiterIsEnabled, false 
#         uint128 inboundRateLimiterCapacity, 0 
#         uint128 inboundRateLimiterRate 0 
if [ "$SKIP_ZKSYNC" != "true" ]; then
    forge script ./script/ConfigurePool.s.sol:ConfigurePoolScript --rpc-url ${SEPOLIA_RPC_URL} --account code023 --broadcast --sig "run(address,uint64,address,address,bool,uint128,uint128,bool,uint128,uint128)" ${SEPOLIA_POOL_ADDRESS} ${ZKSYNC_SEPOLIA_CHAIN_SELECTOR} ${ZKSYNC_POOL_ADDRESS} ${ZKSYNC_REBASE_TOKEN_ADDRESS} false 0 0 false 0 0
else
    echo "WARNING: Skipping Sepolia pool configuration (no ZKsync addresses)"
fi

# Deposit funds to the vault
echo "Depositing funds to the vault on Sepolia..."
cast send ${VAULT_ADDRESS} --value ${AMOUNT} --rpc-url ${SEPOLIA_RPC_URL} --account code023 "deposit()"

# Wait a beat for some interest to accrue

if [ "$SKIP_ZKSYNC" != "true" ]; then
    # Configure the pool on ZKsync
    echo "Configuring the pool on ZKsync..."
    cast send ${ZKSYNC_POOL_ADDRESS}  --rpc-url ${ZKSYNC_SEPOLIA_RPC_URL} --account code023 "applyChainUpdates(uint64[],(uint64,bytes[],bytes,(bool,uint128,uint128),(bool,uint128,uint128))[])" "[${SEPOLIA_CHAIN_SELECTOR}]" "[(${SEPOLIA_CHAIN_SELECTOR},[$(cast abi-encode "f(address)" ${SEPOLIA_POOL_ADDRESS})],$(cast abi-encode "f(address)" ${SEPOLIA_REBASE_TOKEN_ADDRESS}),(false,0,0),(false,0,0))]"
else
    echo "WARNING: Skipping ZKsync pool configuration"
fi

if [ "$SKIP_ZKSYNC" != "true" ]; then
    # Bridge the funds using the script to zksync
    echo "Bridging the funds using the script to ZKsync..."
    SEPOLIA_BALANCE_BEFORE=$(cast balance $(cast wallet address --account code023) --erc20 ${SEPOLIA_REBASE_TOKEN_ADDRESS} --rpc-url ${SEPOLIA_RPC_URL})
    echo "Sepolia balance before bridging: $SEPOLIA_BALANCE_BEFORE"
    forge script ./script/BridgeTokens.s.sol:BridgeTokensScript --rpc-url ${SEPOLIA_RPC_URL} --account code023 --broadcast --sig "run(address,uint64,address,uint256,address,address)" $(cast wallet address --account code023) ${ZKSYNC_SEPOLIA_CHAIN_SELECTOR} ${SEPOLIA_REBASE_TOKEN_ADDRESS} ${AMOUNT} ${SEPOLIA_LINK_ADDRESS} ${SEPOLIA_ROUTER}
    echo "Funds bridged to ZKsync"
else
    echo "WARNING: Skipping bridge operation (ZKsync deployment failed)"
    SEPOLIA_BALANCE_BEFORE=$(cast balance $(cast wallet address --account code023) --erc20 ${SEPOLIA_REBASE_TOKEN_ADDRESS} --rpc-url ${SEPOLIA_RPC_URL})
    echo "Sepolia balance (no bridging): $SEPOLIA_BALANCE_BEFORE"
fi
SEPOLIA_BALANCE_AFTER=$(cast balance $(cast wallet address --account code023) --erc20 ${SEPOLIA_REBASE_TOKEN_ADDRESS} --rpc-url ${SEPOLIA_RPC_URL})
echo "Sepolia balance after bridging: $SEPOLIA_BALANCE_AFTER"

ZKSYNC_BALANCE_AFTER=$(cast balance $(cast wallet address --account code023) --erc20 ${ZKSYNC_REBASE_TOKEN_ADDRESS} --rpc-url ${ZKSYNC_SEPOLIA_RPC_URL})
echo "ZKsync balance after bridging: $ZKSYNC_BALANCE_AFTER"

